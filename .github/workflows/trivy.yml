name: Trivy

on:
  push:
    branches:
    - "main"
  schedule:
  - cron: '55 17 * * 5'

permissions:
  contents: read

jobs:
  image-scan:
    permissions:
      contents: read  # for actions/checkout to fetch code
    name: Image Scan
    runs-on: ubuntu-22.04
    steps:
    - name: Checkout code
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2

    - name: Build an image from Dockerfile
      run: |
        IMAGE=envoy-proxy/gateway-dev TAG=${{ github.sha }} make image

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@dc5a429b52fcf669ce959baa2c2dd26090d2a6c4  # v0.32.0
      with:
        image-ref: envoy-proxy/gateway-dev:${{ github.sha }}
        exit-code: '1'
        ignore-unfixed: true
