name: Scorecard supply-chain security
on:
  branch_protection_rule:
  schedule:
  - cron: '33 13 * * 5'
  push:
    branches:
    - "main"

permissions:
  contents: read


jobs:
  analysis:
    name: Scorecard analysis
    runs-on: ubuntu-22.04
    permissions:
      security-events: write
      id-token: write

    steps:
    - name: "Checkout code"
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
      with:
        persist-credentials: false

    - name: "Run analysis"
      uses: ossf/scorecard-action@05b42c624433fc40578a4040d5cf5e36ddca8cde  # v2.4.2
      with:
        results_file: results.sarif
        results_format: sarif
        publish_results: true

    - name: "Upload artifact"
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02  # v4.6.2
      with:
        name: SARIF file
        path: results.sarif
        retention-days: 5

    - name: "Upload to code-scanning"
      uses: github/codeql-action/upload-sarif@181d5eefc20863364f96762470ba6f862bdef56b  # v3.29.2
      with:
        sarif_file: results.sarif
