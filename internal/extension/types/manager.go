// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package types

import (
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"

	egv1a1 "github.com/envoyproxy/gateway/api/v1alpha1"
)

// Manager handles and maintains registered extensions and returns clients for
// different Hook types.
type Manager interface {
	// HasExtension checks to see whether a given Group and Kind has an
	// associated extension registered for it.
	//
	// If a Group and Kind is registered with an extension, then it should
	// return true, otherwise return false.
	HasExtension(g gwapiv1.Group, k gwapiv1.Kind) bool

	// GetPreXDSHookClient checks if the registered extension makes use of a particular hook type that modifies inputs
	// that are used to generate an xDS resource.
	// If the extension makes use of the hook then the XDS Hook Client is returned. If it does not support
	// the hook type then nil is returned
	GetPreXDSHookClient(xdsHookType egv1a1.XDSTranslatorHook) (XDSHookClient, error)

	// GetPostXDSHookClient checks if the registered extension makes use of a particular hook type that modifies
	// xDS resources after they are generated by Envoy Gateway.
	// If the extension makes use of the hook then the XDS Hook Client is returned. If it does not support
	// the hook type then nil is returned
	GetPostXDSHookClient(xdsHookType egv1a1.XDSTranslatorHook) (XDSHookClient, error)

	// FailOpen returns true if the extension manager is configured to fail open, and false otherwise.
	FailOpen() bool

	// EnablePostTranslateListenersAndRoutes returns whether the extension manager should
	// include listeners and routes in PostTranslateModifyHook calls.
	EnablePostTranslateListenersAndRoutes() bool
}
