clientTrafficPolicies:
  - apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: ClientTrafficPolicy
    metadata:
      namespace: envoy-gateway
      name: target-gateway
    spec:
      targetRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway
        sectionName: http-1
      timeout:
        http:
          requestReceivedTimeout: "5s"
          streamIdleTimeout: "1h"
gateways:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    metadata:
      namespace: envoy-gateway
      name: gateway
    spec:
      gatewayClassName: envoy-gateway-class
      listeners:
        - name: http-1
          protocol: HTTP
          port: 80
          allowedRoutes:
            namespaces:
              from: Same
        - name: http-2
          protocol: HTTP
          port: 8080
          allowedRoutes:
            namespaces:
              from: Same

