envoyExtensionPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    namespace: envoy-gateway
    name: target-gateway-with-accepted-truncated-ancestors
  spec:
    targetRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-2
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-3
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-4
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-5
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-6
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-7
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-8
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-9
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-10
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-11
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-12
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-13
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-14
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-15
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-16
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-17
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-18
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    namespace: envoy-gateway
    name: target-gateway-with-attachment-conflict-truncated-ancestors
  spec:
    targetRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-2
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-3
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-4
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-5
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-6
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-7
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-8
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-9
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-10
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-11
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-12
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-13
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-14
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-15
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-16
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-17
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-18
gateways:
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-2
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-3
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-4
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-5
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-6
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-7
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-8
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-9
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-10
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-11
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-12
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-13
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-14
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-15
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-16
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-17
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-18
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
