envoyProxyForGatewayClass:
  apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyProxy
  metadata:
    namespace: envoy-gateway-system
    name: test
  spec:
    luaValidation: Syntax
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: All
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-1
  spec:
    hostnames:
    - www.example.com
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/foo"
      backendRefs:
      - name: service-1
        port: 8080
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-2
  spec:
    hostnames:
    - www.example.com
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/foo"
      backendRefs:
      - name: service-1
        port: 8080
envoyextensionpolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    namespace: default
    name: policy-for-http-route
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
    lua:
    - type: Inline  # Lua with external library and UnknownApi() call but correct syntax so should be accepted
      inline: |
        local json = require("json")
        function envoy_on_response(response_handle)
            local content_type = response_handle:headers():get("content-type")
            if content_type and string.find(content_type, "application/json", 1, true) then
                response_handle:body():setBytes(0, response_handle:body():length())
                response_handle:UnknownApi()
                local response_body = response_handle:body():getBytes(0, response_handle:body():length())
                if response_body and #response_body > 0 then
                    local parsed_json = json.decode(response_body)
                    if type(parsed_json) == "table" then
                        response_handle:logInfo("Successfully parsed JSON response.")
                    else
                        response_handle:logWarn("Parsed JSON is not a table, or unexpected format.")
                    end
                end
            end
            return envoy.lua.ResponseStatus.Continue
        end
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    namespace: default
    name: policy-for-http-route-2
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-2
    lua:
    - type: Inline  # Invalid Lua syntax (missing then keyword in if statement) so should be rejected
      inline: |
        function envoy_on_response(response_handle)
          local value = 10
          if value > 5
            print("Value is greater than 5")
          end
        end
