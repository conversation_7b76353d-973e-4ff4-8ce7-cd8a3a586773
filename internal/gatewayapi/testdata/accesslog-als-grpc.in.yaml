envoyProxyForGatewayClass:
  apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyProxy
  metadata:
    namespace: envoy-gateway-system
    name: als-backend
  spec:
    telemetry:
      accessLog:
        settings:
          - sinks:
              - type: ALS
                als:
                  backendRefs:
                    - name: access-logger
                      namespace: default
                      port: 50051
                  type: HTTP
gateways:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    metadata:
      namespace: envoy-gateway
      name: gateway-1
    spec:
      gatewayClassName: envoy-gateway-class
      listeners:
        - name: http
          protocol: HTTP
          port: 80
          allowedRoutes:
            namespaces:
              from: Same
services:
  - apiVersion: v1
    kind: Service
    metadata:
      name: access-logger
      namespace: default
    spec:
      clusterIP: ***********
      ports:
        - port: 50051
          name: grpc
          protocol: TCP
          targetPort: 50051
