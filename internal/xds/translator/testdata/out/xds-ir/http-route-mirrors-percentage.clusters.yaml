- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: mirror-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: mirror-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
