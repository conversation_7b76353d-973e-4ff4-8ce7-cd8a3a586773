- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: httproute/default/httproute-1/rule/1/backend/0
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: httproute/default/httproute-1/rule/1/backend/0
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: httproute/default/httproute-1/rule/1/backend/1
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: httproute/default/httproute-1/rule/1/backend/1
  perConnectionBufferLimitBytes: 32768
  type: EDS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        httpProtocolOptions: {}
      httpFilters:
      - name: envoy.filters.http.credential_injector/httproutefilter/default/credential-injection-1
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.filters.http.credential_injector.v3.CredentialInjector
          credential:
            name: envoy.http.injected_credentials.generic
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.http.injected_credentials.generic.v3.Generic
              credential:
                name: credential_injector/credential/httproutefilter/default/credential-injection-1
                sdsConfig:
                  ads: {}
                  resourceApiVersion: V3
      - name: envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
