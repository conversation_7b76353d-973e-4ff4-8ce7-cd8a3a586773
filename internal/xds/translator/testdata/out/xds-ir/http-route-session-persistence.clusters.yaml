- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: regex-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: regex-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
