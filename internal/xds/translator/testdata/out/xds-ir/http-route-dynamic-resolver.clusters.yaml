- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  clusterType:
    name: httproute/default/httproute-1/rule/0
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.clusters.dynamic_forward_proxy.v3.ClusterConfig
      dnsCacheConfig:
        dnsLookupFamily: V4_PREFERRED
        dnsRefreshRate: 30s
        name: httproute/default/httproute-1/rule/0
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  ignoreHealthOnHostRemoval: true
  lbPolicy: CLUSTER_PROVIDED
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.cluster_provided
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.cluster_provided.v3.ClusterProvided
  name: httproute/default/httproute-1/rule/0
  perConnectionBufferLimitBytes: 32768
  transportSocket:
    name: envoy.transport_sockets.tls
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
      commonTlsContext:
        combinedValidationContext:
          defaultValidationContext: {}
          validationContextSdsSecretConfig:
            name: backend-1/default-ca
            sdsConfig:
              ads: {}
              resourceApiVersion: V3
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
      upstreamHttpProtocolOptions:
        autoSanValidation: true
        autoSni: true
