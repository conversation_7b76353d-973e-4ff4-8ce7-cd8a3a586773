- clusterName: first-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: first-route-dest/backend/0
- clusterName: second-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: second-route-dest/backend/0
