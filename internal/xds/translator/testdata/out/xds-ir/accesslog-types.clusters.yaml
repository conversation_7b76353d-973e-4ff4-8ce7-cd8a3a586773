- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: direct-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: direct-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: accesslog_als_0_1
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: accesslog_als_0_1
  perConnectionBufferLimitBytes: 32768
  type: EDS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: accesslog_als_0_2
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: accesslog_als_0_2
  perConnectionBufferLimitBytes: 32768
  type: EDS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: accesslog_als_1_1
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: accesslog_als_1_1
  perConnectionBufferLimitBytes: 32768
  type: EDS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: accesslog_als_1_2
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: accesslog_als_1_2
  perConnectionBufferLimitBytes: 32768
  type: EDS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: accesslog_als_2_1
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: accesslog_als_2_1
  perConnectionBufferLimitBytes: 32768
  type: EDS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: accesslog_als_2_2
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: accesslog_als_2_2
  perConnectionBufferLimitBytes: 32768
  type: EDS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  dnsRefreshRate: 30s
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadAssignment:
    clusterName: accesslog_otel_0_3
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: otel-collector.monitoring.svc.cluster.local
              portValue: 4317
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality:
        region: accesslog_otel_0_3/backend/0
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: accesslog_otel_0_3
  perConnectionBufferLimitBytes: 32768
  respectDnsTtl: true
  type: STRICT_DNS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  dnsRefreshRate: 30s
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadAssignment:
    clusterName: accesslog_otel_1_3
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: otel-collector.monitoring.svc.cluster.local
              portValue: 4317
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality:
        region: accesslog_otel_1_3/backend/0
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: accesslog_otel_1_3
  perConnectionBufferLimitBytes: 32768
  respectDnsTtl: true
  type: STRICT_DNS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  dnsRefreshRate: 30s
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadAssignment:
    clusterName: accesslog_otel_2_3
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: otel-collector.monitoring.svc.cluster.local
              portValue: 4317
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality:
        region: accesslog_otel_2_3/backend/0
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: accesslog_otel_2_3
  perConnectionBufferLimitBytes: 32768
  respectDnsTtl: true
  type: STRICT_DNS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
