- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V6_ONLY
  dnsRefreshRate: 5s
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadAssignment:
    clusterName: httproute/default/httproute-1/rule/0
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: grpc-infra-backend.gateway-conformance-infra.svc.cluster.local
              portValue: 8080
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality: {}
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: httproute/default/httproute-1/rule/0
  perConnectionBufferLimitBytes: 32768
  type: STRICT_DNS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V6_ONLY
  dnsRefreshRate: 5s
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadAssignment:
    clusterName: grpcroute/default/grpcroute-1/rule/0
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: grpc-infra-backend.gateway-conformance-infra.svc.cluster.local
              portValue: 8080
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality: {}
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: grpcroute/default/grpcroute-1/rule/0
  perConnectionBufferLimitBytes: 32768
  type: STRICT_DNS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  dnsRefreshRate: 30s
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadAssignment:
    clusterName: securitypolicy/envoy-gateway/policy-for-gateway-1/extauth/0
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: backend-v3.gateway-conformance-infra.svc.cluster.local
              portValue: 8080
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality: {}
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: securitypolicy/envoy-gateway/policy-for-gateway-1/extauth/0
  perConnectionBufferLimitBytes: 32768
  respectDnsTtl: true
  type: STRICT_DNS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: ALL
  dnsRefreshRate: 5s
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadAssignment:
    clusterName: envoyextensionpolicy/default/policy-for-httproute/extproc/0
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: backend-v2.gateway-conformance-infra.svc.cluster.local
              portValue: 9090
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality: {}
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: envoyextensionpolicy/default/policy-for-httproute/extproc/0
  perConnectionBufferLimitBytes: 32768
  respectDnsTtl: true
  type: STRICT_DNS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_ONLY
  dnsRefreshRate: 30s
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadAssignment:
    clusterName: accesslog_otel_0_1
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: otel-collector.default.svc.cluster.local
              portValue: 4317
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality:
        region: accesslog-0/backend/0
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: accesslog_otel_0_1
  perConnectionBufferLimitBytes: 32768
  respectDnsTtl: true
  type: STRICT_DNS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
