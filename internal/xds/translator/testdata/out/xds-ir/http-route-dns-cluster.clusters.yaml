- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  dnsRefreshRate: 30s
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadAssignment:
    clusterName: first-route-dest
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: foo.bar
              portValue: 50000
        loadBalancingWeight: 1
      - endpoint:
          address:
            socketAddress:
              address: bar.foo
              portValue: 50001
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality:
        region: first-route-dest/backend/0
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: first-route-dest
  perConnectionBufferLimitBytes: 32768
  respectDnsTtl: true
  type: STRICT_DNS
