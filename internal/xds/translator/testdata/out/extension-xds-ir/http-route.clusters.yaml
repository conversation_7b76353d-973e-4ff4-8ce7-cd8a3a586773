- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: first-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: first-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- loadAssignment:
    clusterName: mock-extension-injected-cluster
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: exampleservice.examplenamespace.svc.cluster.local
              portValue: 5000
  name: mock-extension-injected-cluster
