- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  ignoreHealthOnHostRemoval: true
  lbPolicy: CLUSTER_PROVIDED
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: custom-backend-dest
  originalDstLbConfig:
    httpHeaderName: x-gateway-destination-endpoint
    useHttpHeader: true
  perConnectionBufferLimitBytes: 32768
  type: ORIGINAL_DST
