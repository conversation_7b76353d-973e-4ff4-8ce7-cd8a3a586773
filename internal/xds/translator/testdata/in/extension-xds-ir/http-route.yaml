http:
- name: "first-listener"
  address: "0.0.0.0"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "first-route"
    hostname: "*"
    headerMatches:
    - name: user
      stringMatch:
      exact: "jason"
    - name: test
      stringMatch:
      suffix: "end"
    queryParamMatches:
    - name: "debug"
      exact: "yes"
    destination:
      name: "first-route-dest"
      settings:
      - endpoints:
        - hostname: "example.com"
          host: "*******"
          port: 50000
        name: "first-route-dest/backend/0"
