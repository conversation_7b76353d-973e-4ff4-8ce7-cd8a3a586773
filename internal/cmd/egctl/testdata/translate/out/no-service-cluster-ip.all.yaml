xds:
  envoy-gateway-system/eg:
    configs:
    - '@type': type.googleapis.com/envoy.admin.v3.BootstrapConfigDump
      bootstrap:
        admin:
          accessLog:
          - name: envoy.access_loggers.file
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
              path: /dev/null
          address:
            socketAddress:
              address: 127.0.0.1
              portValue: 19000
        clusterManager:
          localClusterName: local_cluster
        dynamicResources:
          adsConfig:
            apiType: DELTA_GRPC
            grpcServices:
            - envoyGrpc:
                clusterName: xds_cluster
            setNodeOnFirstMessageOnly: true
            transportApiVersion: V3
          cdsConfig:
            ads: {}
            resourceApiVersion: V3
          ldsConfig:
            ads: {}
            resourceApiVersion: V3
        layeredRuntime:
          layers:
          - name: global_config
            staticLayer:
              envoy.restart_features.use_eds_cache_for_ads: true
              re2.max_program_size.error_level: 4294967295
              re2.max_program_size.warn_level: 1000
        node:
          locality:
            zone: $(ENVOY_SERVICE_ZONE)
        overloadManager:
          refreshInterval: 0.250s
          resourceMonitors:
          - name: envoy.resource_monitors.global_downstream_max_connections
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.resource_monitors.downstream_connections.v3.DownstreamConnectionsConfig
              maxActiveDownstreamConnections: "50000"
        staticResources:
          clusters:
          - connectTimeout: 0.250s
            loadAssignment:
              clusterName: prometheus_stats
              endpoints:
              - lbEndpoints:
                - endpoint:
                    address:
                      socketAddress:
                        address: 127.0.0.1
                        portValue: 19000
            name: prometheus_stats
            type: STATIC
          - connectTimeout: 10s
            loadAssignment:
              clusterName: local_cluster
              endpoints:
              - lbEndpoints:
                - endpoint:
                    address:
                      socketAddress:
                        address: 127.0.0.1
                        portValue: 10080
                  loadBalancingWeight: 1
                loadBalancingWeight: 1
                locality:
                  zone: $(ENVOY_SERVICE_ZONE)
            name: local_cluster
            type: STATIC
          - connectTimeout: 10s
            loadAssignment:
              clusterName: xds_cluster
              endpoints:
              - lbEndpoints:
                - endpoint:
                    address:
                      socketAddress:
                        address: envoy-gateway
                        portValue: 18000
                  loadBalancingWeight: 1
                loadBalancingWeight: 1
            name: xds_cluster
            transportSocket:
              name: envoy.transport_sockets.tls
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
                commonTlsContext:
                  tlsCertificateSdsSecretConfigs:
                  - name: xds_certificate
                    sdsConfig:
                      pathConfigSource:
                        path: /sds/xds-certificate.json
                      resourceApiVersion: V3
                  tlsParams:
                    tlsMaximumProtocolVersion: TLSv1_3
                  validationContextSdsSecretConfig:
                    name: xds_trusted_ca
                    sdsConfig:
                      pathConfigSource:
                        path: /sds/xds-trusted-ca.json
                      resourceApiVersion: V3
            type: STRICT_DNS
            typedExtensionProtocolOptions:
              envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
                '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
                explicitHttpConfig:
                  http2ProtocolOptions:
                    connectionKeepalive:
                      interval: 30s
                      timeout: 5s
          listeners:
          - address:
              socketAddress:
                address: 0.0.0.0
                portValue: 19001
            bypassOverloadManager: true
            filterChains:
            - filters:
              - name: envoy.filters.network.http_connection_manager
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  httpFilters:
                  - name: envoy.filters.http.router
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                  normalizePath: true
                  routeConfig:
                    name: local_route
                    virtualHosts:
                    - domains:
                      - '*'
                      name: prometheus_stats
                      routes:
                      - match:
                          headers:
                          - name: :method
                            stringMatch:
                              exact: GET
                          path: /stats/prometheus
                        route:
                          cluster: prometheus_stats
                  statPrefix: eg-stats-http
            name: envoy-gateway-proxy-stats-0.0.0.0-19001
    - '@type': type.googleapis.com/envoy.admin.v3.EndpointsConfigDump
      dynamicEndpointConfigs:
      - endpointConfig:
          '@type': type.googleapis.com/envoy.config.endpoint.v3.ClusterLoadAssignment
          clusterName: httproute/envoy-gateway-system/routes/rule/0
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: *******
                    portValue: 8080
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: httproute/envoy-gateway-system/routes/rule/0/backend/0
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Service
                    name: backend
                    namespace: envoy-gateway-system
                    sectionName: "8080"
    - '@type': type.googleapis.com/envoy.admin.v3.ClustersConfigDump
      dynamicActiveClusters:
      - cluster:
          '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
          circuitBreakers:
            thresholds:
            - maxRetries: 1024
          commonLbConfig: {}
          connectTimeout: 10s
          dnsLookupFamily: V4_PREFERRED
          edsClusterConfig:
            edsConfig:
              ads: {}
              resourceApiVersion: V3
            serviceName: httproute/envoy-gateway-system/routes/rule/0
          ignoreHealthOnHostRemoval: true
          lbPolicy: LEAST_REQUEST
          loadBalancingPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.least_request
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                  localityLbConfig:
                    localityWeightedLbConfig: {}
          metadata:
            filterMetadata:
              envoy-gateway:
                resources:
                - kind: HTTPRoute
                  name: routes
                  namespace: envoy-gateway-system
          name: httproute/envoy-gateway-system/routes/rule/0
          perConnectionBufferLimitBytes: 32768
          type: EDS
    - '@type': type.googleapis.com/envoy.admin.v3.ListenersConfigDump
      dynamicListeners:
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 19003
            bypassOverloadManager: true
            filterChains:
            - filters:
              - name: envoy.filters.network.http_connection_manager
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  httpFilters:
                  - name: envoy.filters.http.health_check
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.health_check.v3.HealthCheck
                      headers:
                      - name: :path
                        stringMatch:
                          exact: /ready
                      passThroughMode: false
                  - name: envoy.filters.http.router
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                      suppressEnvoyHeaders: true
                  routeConfig:
                    name: ready_route
                    virtualHosts:
                    - domains:
                      - '*'
                      name: ready_route
                      routes:
                      - directResponse:
                          status: 500
                        match:
                          prefix: /
                  statPrefix: eg-ready-http
            name: envoy-gateway-proxy-ready-0.0.0.0-19003
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            accessLog:
            - filter:
                responseFlagFilter:
                  flags:
                  - NR
              name: envoy.access_loggers.file
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                logFormat:
                  jsonFormat:
                    :authority: '%REQ(:AUTHORITY)%'
                    bytes_received: '%BYTES_RECEIVED%'
                    bytes_sent: '%BYTES_SENT%'
                    connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                    downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                    downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                    duration: '%DURATION%'
                    method: '%REQ(:METHOD)%'
                    protocol: '%PROTOCOL%'
                    requested_server_name: '%REQUESTED_SERVER_NAME%'
                    response_code: '%RESPONSE_CODE%'
                    response_code_details: '%RESPONSE_CODE_DETAILS%'
                    response_flags: '%RESPONSE_FLAGS%'
                    route_name: '%ROUTE_NAME%'
                    start_time: '%START_TIME%'
                    upstream_cluster: '%UPSTREAM_CLUSTER%'
                    upstream_host: '%UPSTREAM_HOST%'
                    upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                    upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                    user-agent: '%REQ(USER-AGENT)%'
                    x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                    x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                    x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                    x-request-id: '%REQ(X-REQUEST-ID)%'
                path: /dev/stdout
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 10080
            defaultFilterChain:
              filters:
              - name: envoy.filters.network.http_connection_manager
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  accessLog:
                  - name: envoy.access_loggers.file
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                      logFormat:
                        jsonFormat:
                          :authority: '%REQ(:AUTHORITY)%'
                          bytes_received: '%BYTES_RECEIVED%'
                          bytes_sent: '%BYTES_SENT%'
                          connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                          downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                          downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                          duration: '%DURATION%'
                          method: '%REQ(:METHOD)%'
                          protocol: '%PROTOCOL%'
                          requested_server_name: '%REQUESTED_SERVER_NAME%'
                          response_code: '%RESPONSE_CODE%'
                          response_code_details: '%RESPONSE_CODE_DETAILS%'
                          response_flags: '%RESPONSE_FLAGS%'
                          route_name: '%ROUTE_NAME%'
                          start_time: '%START_TIME%'
                          upstream_cluster: '%UPSTREAM_CLUSTER%'
                          upstream_host: '%UPSTREAM_HOST%'
                          upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                          upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                          user-agent: '%REQ(USER-AGENT)%'
                          x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                          x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                          x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                          x-request-id: '%REQ(X-REQUEST-ID)%'
                      path: /dev/stdout
                  commonHttpProtocolOptions:
                    headersWithUnderscoresAction: REJECT_REQUEST
                  http2ProtocolOptions:
                    initialConnectionWindowSize: 1048576
                    initialStreamWindowSize: 65536
                    maxConcurrentStreams: 100
                  httpFilters:
                  - name: envoy.filters.http.router
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                      suppressEnvoyHeaders: true
                  mergeSlashes: true
                  normalizePath: true
                  pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
                  rds:
                    configSource:
                      ads: {}
                      resourceApiVersion: V3
                    routeConfigName: envoy-gateway-system/eg/http
                  serverHeaderTransformation: PASS_THROUGH
                  statPrefix: http-10080
                  useRemoteAddress: true
              name: envoy-gateway-system/eg/http
            maxConnectionsToAcceptPerSocketEvent: 1
            name: envoy-gateway-system/eg/http
            perConnectionBufferLimitBytes: 32768
    - '@type': type.googleapis.com/envoy.admin.v3.RoutesConfigDump
      dynamicRouteConfigs:
      - routeConfig:
          '@type': type.googleapis.com/envoy.config.route.v3.RouteConfiguration
          ignorePortInHostMatching: true
          name: envoy-gateway-system/eg/http
          virtualHosts:
          - domains:
            - '*'
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Gateway
                    name: eg
                    namespace: envoy-gateway-system
                    sectionName: http
            name: envoy-gateway-system/eg/http/*
            routes:
            - match:
                pathSeparatedPrefix: /service
              metadata:
                filterMetadata:
                  envoy-gateway:
                    resources:
                    - kind: HTTPRoute
                      name: routes
                      namespace: envoy-gateway-system
              name: httproute/envoy-gateway-system/routes/rule/0/match/0/*
              route:
                cluster: httproute/envoy-gateway-system/routes/rule/0
                upgradeConfigs:
                - upgradeType: websocket
