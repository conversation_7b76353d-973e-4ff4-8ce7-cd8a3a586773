gatewayClass:
  apiVersion: gateway.networking.k8s.io/v1
  kind: GatewayClass
  metadata:
    creationTimestamp: null
    name: eg
  spec:
    controllerName: gateway.envoyproxy.io/gatewayclass-controller
  status:
    conditions:
    - lastTransitionTime: null
      message: Valid GatewayClass
      reason: Accepted
      status: "True"
      type: Accepted
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: eg
    namespace: envoy-gateway-system
  spec:
    gatewayClassName: eg
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: backend
    namespace: envoy-gateway-system
  spec:
    hostnames:
    - www.example.com
    parentRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: eg
    rules:
    - backendRefs:
      - group: ""
        kind: Service
        name: backend
        port: 3000
        weight: 1
      matches:
      - path:
          type: PathPrefix
          value: /
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: eg
xds:
  envoy-gateway-system/eg:
    '@type': type.googleapis.com/envoy.admin.v3.ClustersConfigDump
    dynamicActiveClusters:
    - cluster:
        '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
        circuitBreakers:
          thresholds:
          - maxRetries: 1024
        commonLbConfig: {}
        connectTimeout: 10s
        dnsLookupFamily: V4_PREFERRED
        edsClusterConfig:
          edsConfig:
            ads: {}
            resourceApiVersion: V3
          serviceName: httproute/envoy-gateway-system/backend/rule/0
        ignoreHealthOnHostRemoval: true
        lbPolicy: LEAST_REQUEST
        loadBalancingPolicy:
          policies:
          - typedExtensionConfig:
              name: envoy.load_balancing_policies.least_request
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                localityLbConfig:
                  localityWeightedLbConfig: {}
        metadata:
          filterMetadata:
            envoy-gateway:
              resources:
              - kind: HTTPRoute
                name: backend
                namespace: envoy-gateway-system
        name: httproute/envoy-gateway-system/backend/rule/0
        perConnectionBufferLimitBytes: 32768
        type: EDS
