xds:
  default/eg:
    configs:
    - '@type': type.googleapis.com/envoy.admin.v3.BootstrapConfigDump
      bootstrap:
        admin:
          accessLog:
          - name: envoy.access_loggers.file
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
              path: /dev/null
          address:
            socketAddress:
              address: 127.0.0.1
              portValue: 19000
        clusterManager:
          localClusterName: local_cluster
        dynamicResources:
          adsConfig:
            apiType: DELTA_GRPC
            grpcServices:
            - envoyGrpc:
                clusterName: xds_cluster
            setNodeOnFirstMessageOnly: true
            transportApiVersion: V3
          cdsConfig:
            ads: {}
            resourceApiVersion: V3
          ldsConfig:
            ads: {}
            resourceApiVersion: V3
        layeredRuntime:
          layers:
          - name: global_config
            staticLayer:
              envoy.restart_features.use_eds_cache_for_ads: true
              re2.max_program_size.error_level: 4294967295
              re2.max_program_size.warn_level: 1000
        node:
          locality:
            zone: $(ENVOY_SERVICE_ZONE)
        overloadManager:
          refreshInterval: 0.250s
          resourceMonitors:
          - name: envoy.resource_monitors.global_downstream_max_connections
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.resource_monitors.downstream_connections.v3.DownstreamConnectionsConfig
              maxActiveDownstreamConnections: "50000"
        staticResources:
          clusters:
          - connectTimeout: 0.250s
            loadAssignment:
              clusterName: prometheus_stats
              endpoints:
              - lbEndpoints:
                - endpoint:
                    address:
                      socketAddress:
                        address: 127.0.0.1
                        portValue: 19000
            name: prometheus_stats
            type: STATIC
          - connectTimeout: 10s
            loadAssignment:
              clusterName: local_cluster
              endpoints:
              - lbEndpoints:
                - endpoint:
                    address:
                      socketAddress:
                        address: 127.0.0.1
                        portValue: 10080
                  loadBalancingWeight: 1
                loadBalancingWeight: 1
                locality:
                  zone: $(ENVOY_SERVICE_ZONE)
            name: local_cluster
            type: STATIC
          - connectTimeout: 10s
            loadAssignment:
              clusterName: xds_cluster
              endpoints:
              - lbEndpoints:
                - endpoint:
                    address:
                      socketAddress:
                        address: envoy-gateway
                        portValue: 18000
                  loadBalancingWeight: 1
                loadBalancingWeight: 1
            name: xds_cluster
            transportSocket:
              name: envoy.transport_sockets.tls
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
                commonTlsContext:
                  tlsCertificateSdsSecretConfigs:
                  - name: xds_certificate
                    sdsConfig:
                      pathConfigSource:
                        path: /sds/xds-certificate.json
                      resourceApiVersion: V3
                  tlsParams:
                    tlsMaximumProtocolVersion: TLSv1_3
                  validationContextSdsSecretConfig:
                    name: xds_trusted_ca
                    sdsConfig:
                      pathConfigSource:
                        path: /sds/xds-trusted-ca.json
                      resourceApiVersion: V3
            type: STRICT_DNS
            typedExtensionProtocolOptions:
              envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
                '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
                explicitHttpConfig:
                  http2ProtocolOptions:
                    connectionKeepalive:
                      interval: 30s
                      timeout: 5s
          listeners:
          - address:
              socketAddress:
                address: 0.0.0.0
                portValue: 19001
            bypassOverloadManager: true
            filterChains:
            - filters:
              - name: envoy.filters.network.http_connection_manager
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  httpFilters:
                  - name: envoy.filters.http.router
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                  normalizePath: true
                  routeConfig:
                    name: local_route
                    virtualHosts:
                    - domains:
                      - '*'
                      name: prometheus_stats
                      routes:
                      - match:
                          headers:
                          - name: :method
                            stringMatch:
                              exact: GET
                          path: /stats/prometheus
                        route:
                          cluster: prometheus_stats
                  statPrefix: eg-stats-http
            name: envoy-gateway-proxy-stats-0.0.0.0-19001
    - '@type': type.googleapis.com/envoy.admin.v3.EndpointsConfigDump
      dynamicEndpointConfigs:
      - endpointConfig:
          '@type': type.googleapis.com/envoy.config.endpoint.v3.ClusterLoadAssignment
          clusterName: httproute/default/backend/rule/0
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: *******
                    portValue: 3000
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: httproute/default/backend/rule/0/backend/0
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Service
                    name: backend
                    namespace: default
                    sectionName: "3000"
      - endpointConfig:
          '@type': type.googleapis.com/envoy.config.endpoint.v3.ClusterLoadAssignment
          clusterName: grpcroute/default/backend/rule/0
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: *******
                    portValue: 9000
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: grpcroute/default/backend/rule/0/backend/0
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Service
                    name: backend
                    namespace: default
                    sectionName: "9000"
      - endpointConfig:
          '@type': type.googleapis.com/envoy.config.endpoint.v3.ClusterLoadAssignment
          clusterName: tcproute/default/backend/rule/-1
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: *******
                    portValue: 3000
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: tcproute/default/backend/rule/-1/backend/0
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Service
                    name: backend
                    namespace: default
                    sectionName: "3000"
      - endpointConfig:
          '@type': type.googleapis.com/envoy.config.endpoint.v3.ClusterLoadAssignment
          clusterName: tlsroute/default/backend/rule/-1
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: *******
                    portValue: 3000
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: tlsroute/default/backend/rule/-1/backend/0
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Service
                    name: backend
                    namespace: default
                    sectionName: "3000"
      - endpointConfig:
          '@type': type.googleapis.com/envoy.config.endpoint.v3.ClusterLoadAssignment
          clusterName: udproute/default/backend/rule/-1
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: *******
                    portValue: 3000
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: udproute/default/backend/rule/-1/backend/0
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Service
                    name: backend
                    namespace: default
                    sectionName: "3000"
    - '@type': type.googleapis.com/envoy.admin.v3.ClustersConfigDump
      dynamicActiveClusters:
      - cluster:
          '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
          circuitBreakers:
            thresholds:
            - maxRetries: 1024
          commonLbConfig: {}
          connectTimeout: 10s
          dnsLookupFamily: V4_PREFERRED
          edsClusterConfig:
            edsConfig:
              ads: {}
              resourceApiVersion: V3
            serviceName: httproute/default/backend/rule/0
          ignoreHealthOnHostRemoval: true
          lbPolicy: LEAST_REQUEST
          loadBalancingPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.least_request
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                  localityLbConfig:
                    localityWeightedLbConfig: {}
          metadata:
            filterMetadata:
              envoy-gateway:
                resources:
                - kind: HTTPRoute
                  name: backend
                  namespace: default
          name: httproute/default/backend/rule/0
          perConnectionBufferLimitBytes: 32768
          type: EDS
      - cluster:
          '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
          circuitBreakers:
            thresholds:
            - maxRetries: 1024
          commonLbConfig: {}
          connectTimeout: 10s
          dnsLookupFamily: V4_PREFERRED
          edsClusterConfig:
            edsConfig:
              ads: {}
              resourceApiVersion: V3
            serviceName: grpcroute/default/backend/rule/0
          ignoreHealthOnHostRemoval: true
          lbPolicy: LEAST_REQUEST
          loadBalancingPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.least_request
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                  localityLbConfig:
                    localityWeightedLbConfig: {}
          metadata:
            filterMetadata:
              envoy-gateway:
                resources:
                - kind: GRPCRoute
                  name: backend
                  namespace: default
          name: grpcroute/default/backend/rule/0
          perConnectionBufferLimitBytes: 32768
          type: EDS
          typedExtensionProtocolOptions:
            envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
              '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
              explicitHttpConfig:
                http2ProtocolOptions:
                  initialConnectionWindowSize: 1048576
                  initialStreamWindowSize: 65536
      - cluster:
          '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
          circuitBreakers:
            thresholds:
            - maxRetries: 1024
          commonLbConfig: {}
          connectTimeout: 10s
          dnsLookupFamily: V4_PREFERRED
          edsClusterConfig:
            edsConfig:
              ads: {}
              resourceApiVersion: V3
            serviceName: tcproute/default/backend/rule/-1
          ignoreHealthOnHostRemoval: true
          lbPolicy: LEAST_REQUEST
          loadBalancingPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.least_request
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                  localityLbConfig:
                    localityWeightedLbConfig: {}
          metadata:
            filterMetadata:
              envoy-gateway:
                resources:
                - kind: TCPRoute
                  name: backend
                  namespace: default
          name: tcproute/default/backend/rule/-1
          perConnectionBufferLimitBytes: 32768
          type: EDS
      - cluster:
          '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
          circuitBreakers:
            thresholds:
            - maxRetries: 1024
          commonLbConfig: {}
          connectTimeout: 10s
          dnsLookupFamily: V4_PREFERRED
          edsClusterConfig:
            edsConfig:
              ads: {}
              resourceApiVersion: V3
            serviceName: tlsroute/default/backend/rule/-1
          ignoreHealthOnHostRemoval: true
          lbPolicy: LEAST_REQUEST
          loadBalancingPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.least_request
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                  localityLbConfig:
                    localityWeightedLbConfig: {}
          metadata:
            filterMetadata:
              envoy-gateway:
                resources:
                - kind: TLSRoute
                  name: backend
                  namespace: default
          name: tlsroute/default/backend/rule/-1
          perConnectionBufferLimitBytes: 32768
          type: EDS
      - cluster:
          '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
          circuitBreakers:
            thresholds:
            - maxRetries: 1024
          commonLbConfig: {}
          connectTimeout: 10s
          dnsLookupFamily: V4_PREFERRED
          edsClusterConfig:
            edsConfig:
              ads: {}
              resourceApiVersion: V3
            serviceName: udproute/default/backend/rule/-1
          ignoreHealthOnHostRemoval: true
          lbPolicy: LEAST_REQUEST
          loadBalancingPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.least_request
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                  localityLbConfig:
                    localityWeightedLbConfig: {}
          metadata:
            filterMetadata:
              envoy-gateway:
                resources:
                - kind: UDPRoute
                  name: backend
                  namespace: default
          name: udproute/default/backend/rule/-1
          perConnectionBufferLimitBytes: 32768
          type: EDS
    - '@type': type.googleapis.com/envoy.admin.v3.ListenersConfigDump
      dynamicListeners:
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 19003
            bypassOverloadManager: true
            filterChains:
            - filters:
              - name: envoy.filters.network.http_connection_manager
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  httpFilters:
                  - name: envoy.filters.http.health_check
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.health_check.v3.HealthCheck
                      headers:
                      - name: :path
                        stringMatch:
                          exact: /ready
                      passThroughMode: false
                  - name: envoy.filters.http.router
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                      suppressEnvoyHeaders: true
                  routeConfig:
                    name: ready_route
                    virtualHosts:
                    - domains:
                      - '*'
                      name: ready_route
                      routes:
                      - directResponse:
                          status: 500
                        match:
                          prefix: /
                  statPrefix: eg-ready-http
            name: envoy-gateway-proxy-ready-0.0.0.0-19003
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            accessLog:
            - filter:
                responseFlagFilter:
                  flags:
                  - NR
              name: envoy.access_loggers.file
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                logFormat:
                  jsonFormat:
                    :authority: '%REQ(:AUTHORITY)%'
                    bytes_received: '%BYTES_RECEIVED%'
                    bytes_sent: '%BYTES_SENT%'
                    connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                    downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                    downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                    duration: '%DURATION%'
                    method: '%REQ(:METHOD)%'
                    protocol: '%PROTOCOL%'
                    requested_server_name: '%REQUESTED_SERVER_NAME%'
                    response_code: '%RESPONSE_CODE%'
                    response_code_details: '%RESPONSE_CODE_DETAILS%'
                    response_flags: '%RESPONSE_FLAGS%'
                    route_name: '%ROUTE_NAME%'
                    start_time: '%START_TIME%'
                    upstream_cluster: '%UPSTREAM_CLUSTER%'
                    upstream_host: '%UPSTREAM_HOST%'
                    upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                    upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                    user-agent: '%REQ(USER-AGENT)%'
                    x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                    x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                    x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                    x-request-id: '%REQ(X-REQUEST-ID)%'
                path: /dev/stdout
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 10080
            defaultFilterChain:
              filters:
              - name: envoy.filters.network.http_connection_manager
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  accessLog:
                  - name: envoy.access_loggers.file
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                      logFormat:
                        jsonFormat:
                          :authority: '%REQ(:AUTHORITY)%'
                          bytes_received: '%BYTES_RECEIVED%'
                          bytes_sent: '%BYTES_SENT%'
                          connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                          downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                          downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                          duration: '%DURATION%'
                          method: '%REQ(:METHOD)%'
                          protocol: '%PROTOCOL%'
                          requested_server_name: '%REQUESTED_SERVER_NAME%'
                          response_code: '%RESPONSE_CODE%'
                          response_code_details: '%RESPONSE_CODE_DETAILS%'
                          response_flags: '%RESPONSE_FLAGS%'
                          route_name: '%ROUTE_NAME%'
                          start_time: '%START_TIME%'
                          upstream_cluster: '%UPSTREAM_CLUSTER%'
                          upstream_host: '%UPSTREAM_HOST%'
                          upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                          upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                          user-agent: '%REQ(USER-AGENT)%'
                          x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                          x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                          x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                          x-request-id: '%REQ(X-REQUEST-ID)%'
                      path: /dev/stdout
                  commonHttpProtocolOptions:
                    headersWithUnderscoresAction: REJECT_REQUEST
                  http2ProtocolOptions:
                    initialConnectionWindowSize: 1048576
                    initialStreamWindowSize: 65536
                    maxConcurrentStreams: 100
                  httpFilters:
                  - name: envoy.filters.http.router
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                      suppressEnvoyHeaders: true
                  mergeSlashes: true
                  normalizePath: true
                  pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
                  rds:
                    configSource:
                      ads: {}
                      resourceApiVersion: V3
                    routeConfigName: default/eg/http
                  serverHeaderTransformation: PASS_THROUGH
                  statPrefix: http-10080
                  useRemoteAddress: true
              name: default/eg/http
            maxConnectionsToAcceptPerSocketEvent: 1
            name: default/eg/http
            perConnectionBufferLimitBytes: 32768
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            accessLog:
            - filter:
                responseFlagFilter:
                  flags:
                  - NR
              name: envoy.access_loggers.file
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                logFormat:
                  jsonFormat:
                    :authority: '%REQ(:AUTHORITY)%'
                    bytes_received: '%BYTES_RECEIVED%'
                    bytes_sent: '%BYTES_SENT%'
                    connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                    downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                    downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                    duration: '%DURATION%'
                    method: '%REQ(:METHOD)%'
                    protocol: '%PROTOCOL%'
                    requested_server_name: '%REQUESTED_SERVER_NAME%'
                    response_code: '%RESPONSE_CODE%'
                    response_code_details: '%RESPONSE_CODE_DETAILS%'
                    response_flags: '%RESPONSE_FLAGS%'
                    route_name: '%ROUTE_NAME%'
                    start_time: '%START_TIME%'
                    upstream_cluster: '%UPSTREAM_CLUSTER%'
                    upstream_host: '%UPSTREAM_HOST%'
                    upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                    upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                    user-agent: '%REQ(USER-AGENT)%'
                    x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                    x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                    x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                    x-request-id: '%REQ(X-REQUEST-ID)%'
                path: /dev/stdout
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 8080
            defaultFilterChain:
              filters:
              - name: envoy.filters.network.http_connection_manager
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  accessLog:
                  - name: envoy.access_loggers.file
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                      logFormat:
                        jsonFormat:
                          :authority: '%REQ(:AUTHORITY)%'
                          bytes_received: '%BYTES_RECEIVED%'
                          bytes_sent: '%BYTES_SENT%'
                          connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                          downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                          downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                          duration: '%DURATION%'
                          method: '%REQ(:METHOD)%'
                          protocol: '%PROTOCOL%'
                          requested_server_name: '%REQUESTED_SERVER_NAME%'
                          response_code: '%RESPONSE_CODE%'
                          response_code_details: '%RESPONSE_CODE_DETAILS%'
                          response_flags: '%RESPONSE_FLAGS%'
                          route_name: '%ROUTE_NAME%'
                          start_time: '%START_TIME%'
                          upstream_cluster: '%UPSTREAM_CLUSTER%'
                          upstream_host: '%UPSTREAM_HOST%'
                          upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                          upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                          user-agent: '%REQ(USER-AGENT)%'
                          x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                          x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                          x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                          x-request-id: '%REQ(X-REQUEST-ID)%'
                      path: /dev/stdout
                  commonHttpProtocolOptions:
                    headersWithUnderscoresAction: REJECT_REQUEST
                  http2ProtocolOptions:
                    initialConnectionWindowSize: 1048576
                    initialStreamWindowSize: 65536
                    maxConcurrentStreams: 100
                  httpFilters:
                  - name: envoy.filters.http.grpc_web
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.grpc_web.v3.GrpcWeb
                  - name: envoy.filters.http.grpc_stats
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.grpc_stats.v3.FilterConfig
                      emitFilterState: true
                      statsForAllMethods: true
                  - name: envoy.filters.http.router
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                      suppressEnvoyHeaders: true
                  mergeSlashes: true
                  normalizePath: true
                  pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
                  rds:
                    configSource:
                      ads: {}
                      resourceApiVersion: V3
                    routeConfigName: default/eg/grpc
                  serverHeaderTransformation: PASS_THROUGH
                  statPrefix: http-8080
                  useRemoteAddress: true
              name: default/eg/grpc
            maxConnectionsToAcceptPerSocketEvent: 1
            name: default/eg/grpc
            perConnectionBufferLimitBytes: 32768
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            accessLog:
            - filter:
                responseFlagFilter:
                  flags:
                  - NR
              name: envoy.access_loggers.file
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                logFormat:
                  jsonFormat:
                    :authority: '%REQ(:AUTHORITY)%'
                    bytes_received: '%BYTES_RECEIVED%'
                    bytes_sent: '%BYTES_SENT%'
                    connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                    downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                    downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                    duration: '%DURATION%'
                    method: '%REQ(:METHOD)%'
                    protocol: '%PROTOCOL%'
                    requested_server_name: '%REQUESTED_SERVER_NAME%'
                    response_code: '%RESPONSE_CODE%'
                    response_code_details: '%RESPONSE_CODE_DETAILS%'
                    response_flags: '%RESPONSE_FLAGS%'
                    route_name: '%ROUTE_NAME%'
                    start_time: '%START_TIME%'
                    upstream_cluster: '%UPSTREAM_CLUSTER%'
                    upstream_host: '%UPSTREAM_HOST%'
                    upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                    upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                    user-agent: '%REQ(USER-AGENT)%'
                    x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                    x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                    x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                    x-request-id: '%REQ(X-REQUEST-ID)%'
                path: /dev/stdout
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 1234
            filterChains:
            - filters:
              - name: envoy.filters.network.tcp_proxy
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
                  accessLog:
                  - name: envoy.access_loggers.file
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                      logFormat:
                        jsonFormat:
                          :authority: '%REQ(:AUTHORITY)%'
                          bytes_received: '%BYTES_RECEIVED%'
                          bytes_sent: '%BYTES_SENT%'
                          connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                          downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                          downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                          duration: '%DURATION%'
                          method: '%REQ(:METHOD)%'
                          protocol: '%PROTOCOL%'
                          requested_server_name: '%REQUESTED_SERVER_NAME%'
                          response_code: '%RESPONSE_CODE%'
                          response_code_details: '%RESPONSE_CODE_DETAILS%'
                          response_flags: '%RESPONSE_FLAGS%'
                          route_name: '%ROUTE_NAME%'
                          start_time: '%START_TIME%'
                          upstream_cluster: '%UPSTREAM_CLUSTER%'
                          upstream_host: '%UPSTREAM_HOST%'
                          upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                          upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                          user-agent: '%REQ(USER-AGENT)%'
                          x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                          x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                          x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                          x-request-id: '%REQ(X-REQUEST-ID)%'
                      path: /dev/stdout
                  cluster: tcproute/default/backend/rule/-1
                  statPrefix: tcp-1234
              name: tcproute/default/backend
            maxConnectionsToAcceptPerSocketEvent: 1
            name: default/eg/tcp
            perConnectionBufferLimitBytes: 32768
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            accessLog:
            - filter:
                responseFlagFilter:
                  flags:
                  - NR
              name: envoy.access_loggers.file
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                logFormat:
                  jsonFormat:
                    :authority: '%REQ(:AUTHORITY)%'
                    bytes_received: '%BYTES_RECEIVED%'
                    bytes_sent: '%BYTES_SENT%'
                    connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                    downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                    downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                    duration: '%DURATION%'
                    method: '%REQ(:METHOD)%'
                    protocol: '%PROTOCOL%'
                    requested_server_name: '%REQUESTED_SERVER_NAME%'
                    response_code: '%RESPONSE_CODE%'
                    response_code_details: '%RESPONSE_CODE_DETAILS%'
                    response_flags: '%RESPONSE_FLAGS%'
                    route_name: '%ROUTE_NAME%'
                    start_time: '%START_TIME%'
                    upstream_cluster: '%UPSTREAM_CLUSTER%'
                    upstream_host: '%UPSTREAM_HOST%'
                    upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                    upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                    user-agent: '%REQ(USER-AGENT)%'
                    x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                    x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                    x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                    x-request-id: '%REQ(X-REQUEST-ID)%'
                path: /dev/stdout
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 8443
            filterChains:
            - filterChainMatch:
                serverNames:
                - foo.com
              filters:
              - name: envoy.filters.network.tcp_proxy
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
                  accessLog:
                  - name: envoy.access_loggers.file
                    typedConfig:
                      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                      logFormat:
                        jsonFormat:
                          :authority: '%REQ(:AUTHORITY)%'
                          bytes_received: '%BYTES_RECEIVED%'
                          bytes_sent: '%BYTES_SENT%'
                          connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                          downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                          downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                          duration: '%DURATION%'
                          method: '%REQ(:METHOD)%'
                          protocol: '%PROTOCOL%'
                          requested_server_name: '%REQUESTED_SERVER_NAME%'
                          response_code: '%RESPONSE_CODE%'
                          response_code_details: '%RESPONSE_CODE_DETAILS%'
                          response_flags: '%RESPONSE_FLAGS%'
                          route_name: '%ROUTE_NAME%'
                          start_time: '%START_TIME%'
                          upstream_cluster: '%UPSTREAM_CLUSTER%'
                          upstream_host: '%UPSTREAM_HOST%'
                          upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                          upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                          user-agent: '%REQ(USER-AGENT)%'
                          x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                          x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                          x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                          x-request-id: '%REQ(X-REQUEST-ID)%'
                      path: /dev/stdout
                  cluster: tlsroute/default/backend/rule/-1
                  statPrefix: tls-passthrough-8443
              name: tlsroute/default/backend
            listenerFilters:
            - name: envoy.filters.listener.tls_inspector
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.filters.listener.tls_inspector.v3.TlsInspector
            maxConnectionsToAcceptPerSocketEvent: 1
            name: default/eg/tls-passthrough
            perConnectionBufferLimitBytes: 32768
      - activeState:
          listener:
            '@type': type.googleapis.com/envoy.config.listener.v3.Listener
            accessLog:
            - filter:
                responseFlagFilter:
                  flags:
                  - NR
              name: envoy.access_loggers.file
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                logFormat:
                  jsonFormat:
                    :authority: '%REQ(:AUTHORITY)%'
                    bytes_received: '%BYTES_RECEIVED%'
                    bytes_sent: '%BYTES_SENT%'
                    connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                    downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                    downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                    duration: '%DURATION%'
                    method: '%REQ(:METHOD)%'
                    protocol: '%PROTOCOL%'
                    requested_server_name: '%REQUESTED_SERVER_NAME%'
                    response_code: '%RESPONSE_CODE%'
                    response_code_details: '%RESPONSE_CODE_DETAILS%'
                    response_flags: '%RESPONSE_FLAGS%'
                    route_name: '%ROUTE_NAME%'
                    start_time: '%START_TIME%'
                    upstream_cluster: '%UPSTREAM_CLUSTER%'
                    upstream_host: '%UPSTREAM_HOST%'
                    upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                    upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                    user-agent: '%REQ(USER-AGENT)%'
                    x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                    x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                    x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                    x-request-id: '%REQ(X-REQUEST-ID)%'
                path: /dev/stdout
            address:
              socketAddress:
                address: 0.0.0.0
                portValue: 1234
                protocol: UDP
            listenerFilters:
            - name: envoy.filters.udp_listener.udp_proxy
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.filters.udp.udp_proxy.v3.UdpProxyConfig
                accessLog:
                - name: envoy.access_loggers.file
                  typedConfig:
                    '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                    logFormat:
                      jsonFormat:
                        :authority: '%REQ(:AUTHORITY)%'
                        bytes_received: '%BYTES_RECEIVED%'
                        bytes_sent: '%BYTES_SENT%'
                        connection_termination_details: '%CONNECTION_TERMINATION_DETAILS%'
                        downstream_local_address: '%DOWNSTREAM_LOCAL_ADDRESS%'
                        downstream_remote_address: '%DOWNSTREAM_REMOTE_ADDRESS%'
                        duration: '%DURATION%'
                        method: '%REQ(:METHOD)%'
                        protocol: '%PROTOCOL%'
                        requested_server_name: '%REQUESTED_SERVER_NAME%'
                        response_code: '%RESPONSE_CODE%'
                        response_code_details: '%RESPONSE_CODE_DETAILS%'
                        response_flags: '%RESPONSE_FLAGS%'
                        route_name: '%ROUTE_NAME%'
                        start_time: '%START_TIME%'
                        upstream_cluster: '%UPSTREAM_CLUSTER%'
                        upstream_host: '%UPSTREAM_HOST%'
                        upstream_local_address: '%UPSTREAM_LOCAL_ADDRESS%'
                        upstream_transport_failure_reason: '%UPSTREAM_TRANSPORT_FAILURE_REASON%'
                        user-agent: '%REQ(USER-AGENT)%'
                        x-envoy-origin-path: '%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%'
                        x-envoy-upstream-service-time: '%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%'
                        x-forwarded-for: '%REQ(X-FORWARDED-FOR)%'
                        x-request-id: '%REQ(X-REQUEST-ID)%'
                    path: /dev/stdout
                matcher:
                  onNoMatch:
                    action:
                      name: route
                      typedConfig:
                        '@type': type.googleapis.com/envoy.extensions.filters.udp.udp_proxy.v3.Route
                        cluster: udproute/default/backend/rule/-1
                statPrefix: service
            name: default/eg/udp
    - '@type': type.googleapis.com/envoy.admin.v3.RoutesConfigDump
      dynamicRouteConfigs:
      - routeConfig:
          '@type': type.googleapis.com/envoy.config.route.v3.RouteConfiguration
          ignorePortInHostMatching: true
          name: default/eg/http
          virtualHosts:
          - domains:
            - www.example.com
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Gateway
                    name: eg
                    namespace: default
                    sectionName: http
            name: default/eg/http/www_example_com
            routes:
            - match:
                prefix: /
              metadata:
                filterMetadata:
                  envoy-gateway:
                    resources:
                    - kind: HTTPRoute
                      name: backend
                      namespace: default
              name: httproute/default/backend/rule/0/match/0/www_example_com
              route:
                cluster: httproute/default/backend/rule/0
                upgradeConfigs:
                - upgradeType: websocket
      - routeConfig:
          '@type': type.googleapis.com/envoy.config.route.v3.RouteConfiguration
          ignorePortInHostMatching: true
          name: default/eg/grpc
          virtualHosts:
          - domains:
            - www.grpc-example.com
            metadata:
              filterMetadata:
                envoy-gateway:
                  resources:
                  - kind: Gateway
                    name: eg
                    namespace: default
                    sectionName: grpc
            name: default/eg/grpc/www_grpc-example_com
            routes:
            - match:
                path: /com.example.Things/DoThing
              metadata:
                filterMetadata:
                  envoy-gateway:
                    resources:
                    - kind: GRPCRoute
                      name: backend
                      namespace: default
              name: grpcroute/default/backend/rule/0/match/0/www_grpc-example_com
              route:
                cluster: grpcroute/default/backend/rule/0
