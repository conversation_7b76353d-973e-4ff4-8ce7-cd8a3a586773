xds:
  envoy-gateway-system/eg:
    '@type': type.googleapis.com/envoy.admin.v3.ClustersConfigDump
    dynamicActiveClusters:
    - cluster:
        '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
        circuitBreakers:
          thresholds:
          - maxRetries: 1024
        commonLbConfig: {}
        connectTimeout: 10s
        dnsLookupFamily: V4_PREFERRED
        edsClusterConfig:
          edsConfig:
            ads: {}
            resourceApiVersion: V3
          serviceName: httproute/envoy-gateway-system/backend/rule/0
        ignoreHealthOnHostRemoval: true
        lbPolicy: LEAST_REQUEST
        loadBalancingPolicy:
          policies:
          - typedExtensionConfig:
              name: envoy.load_balancing_policies.least_request
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                localityLbConfig:
                  localityWeightedLbConfig: {}
        metadata:
          filterMetadata:
            envoy-gateway:
              resources:
              - kind: HTTPRoute
                name: backend
                namespace: envoy-gateway-system
        name: httproute/envoy-gateway-system/backend/rule/0
        perConnectionBufferLimitBytes: 32768
        type: EDS
    - cluster:
        '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
        circuitBreakers:
          thresholds:
          - maxRetries: 1024
        commonLbConfig: {}
        connectTimeout: 10s
        dnsLookupFamily: V4_PREFERRED
        dnsRefreshRate: 30s
        ignoreHealthOnHostRemoval: true
        lbPolicy: LEAST_REQUEST
        loadAssignment:
          clusterName: raw_githubusercontent_com_443
          endpoints:
          - lbEndpoints:
            - endpoint:
                address:
                  socketAddress:
                    address: raw.githubusercontent.com
                    portValue: 443
              loadBalancingWeight: 1
            loadBalancingWeight: 1
            locality:
              region: raw_githubusercontent_com_443/backend/-1
        loadBalancingPolicy:
          policies:
          - typedExtensionConfig:
              name: envoy.load_balancing_policies.least_request
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                localityLbConfig:
                  localityWeightedLbConfig: {}
        name: raw_githubusercontent_com_443
        perConnectionBufferLimitBytes: 32768
        respectDnsTtl: true
        transportSocket:
          name: envoy.transport_sockets.tls
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
            commonTlsContext:
              validationContext:
                trustedCa:
                  filename: /etc/ssl/certs/ca-certificates.crt
            sni: raw.githubusercontent.com
        type: STRICT_DNS
