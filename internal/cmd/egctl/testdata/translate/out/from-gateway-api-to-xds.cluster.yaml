xds:
  default/eg:
    '@type': type.googleapis.com/envoy.admin.v3.ClustersConfigDump
    dynamicActiveClusters:
    - cluster:
        '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
        circuitBreakers:
          thresholds:
          - maxRetries: 1024
        commonLbConfig: {}
        connectTimeout: 10s
        dnsLookupFamily: V4_PREFERRED
        edsClusterConfig:
          edsConfig:
            ads: {}
            resourceApiVersion: V3
          serviceName: httproute/default/backend/rule/0
        ignoreHealthOnHostRemoval: true
        lbPolicy: LEAST_REQUEST
        loadBalancingPolicy:
          policies:
          - typedExtensionConfig:
              name: envoy.load_balancing_policies.least_request
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                localityLbConfig:
                  localityWeightedLbConfig: {}
        metadata:
          filterMetadata:
            envoy-gateway:
              resources:
              - kind: HTTPRoute
                name: backend
                namespace: default
        name: httproute/default/backend/rule/0
        perConnectionBufferLimitBytes: 32768
        type: EDS
    - cluster:
        '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
        circuitBreakers:
          thresholds:
          - maxRetries: 1024
        commonLbConfig: {}
        connectTimeout: 10s
        dnsLookupFamily: V4_PREFERRED
        edsClusterConfig:
          edsConfig:
            ads: {}
            resourceApiVersion: V3
          serviceName: grpcroute/default/backend/rule/0
        ignoreHealthOnHostRemoval: true
        lbPolicy: LEAST_REQUEST
        loadBalancingPolicy:
          policies:
          - typedExtensionConfig:
              name: envoy.load_balancing_policies.least_request
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                localityLbConfig:
                  localityWeightedLbConfig: {}
        metadata:
          filterMetadata:
            envoy-gateway:
              resources:
              - kind: GRPCRoute
                name: backend
                namespace: default
        name: grpcroute/default/backend/rule/0
        perConnectionBufferLimitBytes: 32768
        type: EDS
        typedExtensionProtocolOptions:
          envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
            '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
            explicitHttpConfig:
              http2ProtocolOptions:
                initialConnectionWindowSize: 1048576
                initialStreamWindowSize: 65536
    - cluster:
        '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
        circuitBreakers:
          thresholds:
          - maxRetries: 1024
        commonLbConfig: {}
        connectTimeout: 10s
        dnsLookupFamily: V4_PREFERRED
        edsClusterConfig:
          edsConfig:
            ads: {}
            resourceApiVersion: V3
          serviceName: tcproute/default/backend/rule/-1
        ignoreHealthOnHostRemoval: true
        lbPolicy: LEAST_REQUEST
        loadBalancingPolicy:
          policies:
          - typedExtensionConfig:
              name: envoy.load_balancing_policies.least_request
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                localityLbConfig:
                  localityWeightedLbConfig: {}
        metadata:
          filterMetadata:
            envoy-gateway:
              resources:
              - kind: TCPRoute
                name: backend
                namespace: default
        name: tcproute/default/backend/rule/-1
        perConnectionBufferLimitBytes: 32768
        type: EDS
    - cluster:
        '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
        circuitBreakers:
          thresholds:
          - maxRetries: 1024
        commonLbConfig: {}
        connectTimeout: 10s
        dnsLookupFamily: V4_PREFERRED
        edsClusterConfig:
          edsConfig:
            ads: {}
            resourceApiVersion: V3
          serviceName: tlsroute/default/backend/rule/-1
        ignoreHealthOnHostRemoval: true
        lbPolicy: LEAST_REQUEST
        loadBalancingPolicy:
          policies:
          - typedExtensionConfig:
              name: envoy.load_balancing_policies.least_request
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                localityLbConfig:
                  localityWeightedLbConfig: {}
        metadata:
          filterMetadata:
            envoy-gateway:
              resources:
              - kind: TLSRoute
                name: backend
                namespace: default
        name: tlsroute/default/backend/rule/-1
        perConnectionBufferLimitBytes: 32768
        type: EDS
    - cluster:
        '@type': type.googleapis.com/envoy.config.cluster.v3.Cluster
        circuitBreakers:
          thresholds:
          - maxRetries: 1024
        commonLbConfig: {}
        connectTimeout: 10s
        dnsLookupFamily: V4_PREFERRED
        edsClusterConfig:
          edsConfig:
            ads: {}
            resourceApiVersion: V3
          serviceName: udproute/default/backend/rule/-1
        ignoreHealthOnHostRemoval: true
        lbPolicy: LEAST_REQUEST
        loadBalancingPolicy:
          policies:
          - typedExtensionConfig:
              name: envoy.load_balancing_policies.least_request
              typedConfig:
                '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
                localityLbConfig:
                  localityWeightedLbConfig: {}
        metadata:
          filterMetadata:
            envoy-gateway:
              resources:
              - kind: UDPRoute
                name: backend
                namespace: default
        name: udproute/default/backend/rule/-1
        perConnectionBufferLimitBytes: 32768
        type: EDS
