---
title: Gateway
no_list: true
---

<div class="home-header">
  <div class="container">
    <img src="/icons/logo-white.svg" alt="Gateway Logo" class="mb-5" style="height: 120px;">
    <h1>Envoy Gateway</h1>
    <p class="lead">
      Manage your Application and API traffic with Envoy Gateway.
    </p>
    <p>
      Aimed at making it easy to adopt, use, and manage Envoy Proxy.
      Deploy as a Standalone or Kubernetes-based API Gateway, implementing and extending the Kubernetes Gateway API.
    </p>
    <div class="mt-5">
      <a class="btn btn-lg btn-glass me-3" href="/docs/tasks/quickstart/">
        <i class="fas fa-rocket me-2"></i>Get Started
      </a>
      <a class="btn btn-lg btn-glass" href="https://github.com/envoyproxy/gateway">
        <i class="fab fa-github me-2"></i>GitHub
      </a>
    </div>
    <!-- <div class="mt-4">
      <a href="/docs/tasks/quickstart/" class="btn btn-primary btn-lg me-3">Get Started →</a>
    </div> -->
  </div>
</div>

<section class="feature-section">
  <div class="container">
    <div class="row justify-content-center">
      <article class="col-md-4">
        <div class="feature-box">
          <span class="feature-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 4L4 8l8 4 8-4-8-4zM4 12l8 4 8-4M4 16l8 4 8-4"/>
            </svg>
          </span>
          <h3>Built together. Built in the open.</h3>
          <p>
            Gateway is the result of the community coming together to make it easier than ever to leverage Envoy Proxy for your API Gateway needs.
          </p>
        </div>
      </article>
      <article class="col-md-4">
        <div class="feature-box">
          <span class="feature-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"/>
            </svg>
          </span>
          <h3>Get involved in the community</h3>
          <p>
            Join our community on Slack, join the conversation on GitHub, and attend our community meetings. See links in footer for details and meeting notes.
          </p>
        </div>
      </article>
      <article class="col-md-4">
        <div class="feature-box">
          <span class="feature-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
            </svg>
          </span>
          <h3>Got feature ideas?</h3>
          <p>
            We're always looking for feedback on what features you'd like to see in Gateway. Don't hesitate to raise <a href="https://github.com/envoyproxy/gateway/issues">GitHub issues</a> or join <code>#gateway-dev</code> and <code>#gateway-users</code> on Envoy's Slack.
          </p>
        </div>
      </article>
    </div>
  </div>
</section>

<section class="capabilities-section">
  <div class="container">
    <h2 class="text-center mb-5">Capabilities</h2>
    <div class="row justify-content-center">
      <article class="col-md-6 mb-4">
        <div class="feature-card">
          <div class="icon-container">
            <i class="fas fa-sitemap"></i>
          </div>
          <h3>Use Envoy Proxy as an API Gateway</h3>
          <p>Makes it easy to leverage Envoy Proxy as a Kubernetes Gateway. Envoy Gateway implements the Kubernetes Gateway API, and extends it to make it easy to leverage advanced Envoy features, without knowing details of Envoy proxy.</p>
        </div>
      </article>
      <article class="col-md-6 mb-4">
        <div class="feature-card">
          <div class="icon-container">
            <i class="fas fa-shield-alt"></i>
          </div>
          <h3>Security controls made easy</h3>
          <p>Leverage the Envoy Gateway Security Policy to enforce security controls including mTLS, JWT based access control, OIDC integration, API Key based authorization, and more.</p>
        </div>
      </article>
      <article class="col-md-6 mb-4">
        <div class="feature-card">
          <div class="icon-container">
            <i class="fas fa-network-wired"></i>
          </div>
          <h3>Manage traffic</h3>
          <p>Envoy Gateway supports advanced traffic management and control features including rate limiting, retry policies, circuit breaking, timeouts, failover, and more</p>
        </div>
      </article>
      <article class="col-md-6 mb-4">
        <div class="feature-card">
          <div class="icon-container">
            <i class="fas fa-chart-line"></i>
          </div>
          <h3>Observability</h3>
          <p>Envoy Gateway provides a rich set of observability features including metrics, access logging, distributed tracing, and more.</p>
        </div>
      </article>
      <article class="col-md-12 mb-4">
        <div class="feature-card">
          <div class="icon-container">
            <i class="fas fa-brain"></i>
          </div>
          <h3>GenAI Traffic Management</h3>
          <p>As part of the Envoy ecosystem, <strong>Envoy AI Gateway</strong> builds on <strong>Envoy Gateway</strong> to manage GenAI traffic adding features like LLM provider integrations, token-based rate limiting, an OpenAI-compatible API, and provider fallback support, and more. Click the links below to learn more and get involved.</p>
          <div class="mt-4">
            <a class="btn me-3 btn-sm btn-light-purple" href="https://aigateway.envoyproxy.io/">
              <i class="fas fa-rocket"></i>Get Started with Envoy AI Gateway
            </a>
            <a class="btn btn-sm btn-light-purple" href="https://github.com/envoyproxy/aigateway">
              <i class="fab fa-github"></i>View on GitHub
            </a>
          </div>
        </div>
      </article>
    </div>
  </div>
</section>

{{< adopters >}}

{{< ecosystem >}}
